import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { OrgMember } from '../../members/entities/org-member.entity';
import { MediaFile } from '../../media/entities/media-file.entity';

@Entity('private_messages')
export class PrivateMessage {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'bigint' })
  seq: number;

  @Column({ name: 'sender_id' })
  senderId: number;

  @Column({ name: 'receiver_id' })
  receiverId: number;

  @Column({ name: 'encrypted_content', type: 'text', nullable: true })
  encryptedContent: string;

  @Column({ name: 'media_caption', type: 'text', nullable: true })
  caption: string;

  @Column({ name: 'ephemeral_public_key', type: 'text', nullable: true })
  ephemeralPublicKey: string;

  @Column({ name: 'reply_to_message_id', nullable: true })
  replyToMessageId?: number;

  @Column({ name: 'message_index', nullable: true })
  messageIndex: number;

  @Column({ name: 'previous_chain_length', nullable: true })
  previousChainLength: number;

  @Column({ name: 'chain_key_version', nullable: true })
  chainKeyVersion: number;

  @Column({ name: 'sent_at' })
  sentAt: Date;

  @Column({ name: 'delivered_at', nullable: true })
  deliveredAt: Date;

  @Column({ name: 'acknowledged', default: false })
  acknowledged: boolean;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => OrgMember)
  @JoinColumn({ name: 'sender_id' })
  sender: OrgMember;

  @ManyToOne(() => OrgMember)
  @JoinColumn({ name: 'receiver_id' })
  receiver: OrgMember;

  @ManyToOne(() => MediaFile, (file) => file.privateMessages, {
    nullable: true,
  })
  @JoinColumn({ name: 'file_id' })
  file: MediaFile;
}
