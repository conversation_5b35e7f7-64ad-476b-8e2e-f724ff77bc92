import { Injectable, Logger } from '@nestjs/common';
import { GroupMessagesService } from '../services/group-messages.service';
import { EVENT_NAMES } from 'src/common/constants/event-names';
import { OnEvent } from '@nestjs/event-emitter';
import { SendGroupMessageDto } from '../dto/send-group-message.dto';
import { ChatType } from 'src/infrastructure/socket/dto/chat-operation.dto';

// modules/message/handlers/send-message-requested.handler.ts
@Injectable()
export class SendMessageRequestedHandler {
  private readonly logger = new Logger(SendMessageRequestedHandler.name);

  constructor(private readonly groupmessageService: GroupMessagesService) {}

  @OnEvent(EVENT_NAMES.SEND_MESSAGE_REQUESTED, { async: true })
  async handle(event: {
    chatType: string;
    chatId: string;
    senderId: number;
    content: string;
    replyToMessageId?: number;
    nonce: string;
    metadata?: any;
    sentAt?: Date;
    socketId: string;
  }) {
    try {
      if (event.chatType === ChatType.GROUP) {
        const sendMessageDto: SendGroupMessageDto = {
          groupId: parseInt(event.chatId),
          senderId: event.senderId,
          encryptedContent: event.content,
          nonce: event.nonce,
          encryptedMetaData: event.metadata,
          replyToMessageId: event.replyToMessageId,
          sentAt: event.sentAt || new Date(),
        };

        await this.groupmessageService.saveAndBroadcastMessage(
          sendMessageDto,
          true,
        );
      } else if (event.chatType === ChatType.PRIVATE) {
      }
      this.logger.log(
        `Message processed for sender ${event.senderId} in chat ${event.chatId}`,
      );
    } catch (error) {
      this.logger.error(`Failed to process send message request:`, error);
      throw error;
    }
  }
}
