// infrastructure/socket/socket.gateway.ts
import {
  WebSocketGateway,
  OnGatewayConnection,
  OnGatewayDisconnect,
  SubscribeMessage,
  WebSocketServer,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { UseInterceptors, Logger } from '@nestjs/common';
import { WsAuthInterceptor } from './interceptors/ws-auth.interceptor';
import { PubSubService } from '../redis/services/pubsub.service';
import { UserPresenceHandler } from './handlers/user-presence.handler';
import { SystemHandler } from './handlers/system.handler';
import { RoomManagerService, RoomType } from './services/room-manager.service';
import { RoomValidationService } from './services/room-validation.service';
import { ChatOperationHandler } from './handlers/chat-operation.handler';
import {
  JoinMultipleRoomsDto,
  JoinRoomDto,
  LeaveRoomDto,
} from './dto/room-operation.dto';
import {
  SendMessageDto,
  UserTypingDto,
  MessageReadDto,
  DeleteMessageDto,
  DeleteMessagesForEveryoneDto,
  SocketSendMessageDto,
} from './dto/chat-operation.dto';

@WebSocketGateway({
  cors: { origin: '*' },
})
@UseInterceptors(WsAuthInterceptor)
export class SocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer() server: Server;
  private readonly logger = new Logger(SocketGateway.name);

  constructor(
    private readonly userPresenceHandler: UserPresenceHandler,
    private readonly systemHandler: SystemHandler,
    private readonly pubService: PubSubService,
    private readonly roomManager: RoomManagerService,
    private readonly roomValidationService: RoomValidationService,
    private readonly chatOperationHandler: ChatOperationHandler,
  ) {}

  afterInit(server: Server) {
    this.server = server;
    this.roomValidationService.setServer(server);
    this.logger.log('WebSocket server initialized');
  }

  async handleConnection(client: Socket) {
    try {
      const memberId = client.handshake?.auth?.memberId;
      const deviceId =
        client.handshake.auth?.deviceId ||
        client.handshake.headers['x-device-id'];

      if (!deviceId) {
        this.logger.warn(`Missing deviceId for memberId ${memberId}`);
        client.emit('error', {
          code: 'MISSING_DEVICE_ID',
          message: 'Device ID is required for presence tracking',
        });
        client.disconnect();
        return;
      }

      if (typeof deviceId !== 'string' || deviceId.length < 10) {
        this.logger.warn(
          `Invalid deviceId format for memberId ${memberId}: ${deviceId}`,
        );
        client.emit('error', {
          code: 'INVALID_DEVICE_ID',
          message: 'Invalid device ID format',
        });
        client.disconnect();
        return;
      }

      client.data.deviceId = deviceId;
      client.data.memberId = memberId;

      // Join member-specific room
      await this.roomManager.joinRoom(client, RoomType.MEMBER, memberId);

      this.logger.log(
        `Client connected — memberId: ${memberId}, deviceId: ${deviceId}, socketId: ${client.id}`,
      );

      // Set member online in Redis and emit presence event
      await this.pubService.setMemberOnline(memberId, deviceId);

      // Send current status to the connected client
      const currentStatus = await this.pubService.getMemberStatus(memberId);
      client.emit('your-status', { ...currentStatus });

      // Send connection confirmation
      client.emit('connected', {
        memberId,
        deviceId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      this.logger.error('Error handling connection:', error);
      client.emit('error', {
        code: 'CONNECTION_ERROR',
        message: 'Failed to establish connection',
      });
      client.disconnect();
    }
  }

  async handleDisconnect(client: Socket) {
    try {
      const memberId = client.handshake?.auth?.memberId;
      const deviceId =
        client.handshake.auth?.deviceId ||
        client.handshake.headers['x-device-id'];

      this.logger.log(
        `Client disconnected — memberId: ${memberId}, deviceId: ${deviceId}, socketId: ${client.id}`,
      );

      if (memberId && deviceId) {
        await this.pubService.setMemberOffline(memberId, deviceId);
      }
    } catch (error) {
      this.logger.error('Error handling disconnect:', error);
    }
  }

  // ============== ROOM MANAGEMENT ==============

  @SubscribeMessage('join_room')
  async handleJoinRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: JoinRoomDto,
  ) {
    try {
      const roomName = await this.roomManager.joinRoom(
        client,
        RoomType[data.type.toUpperCase()],
        ...data.identifiers,
      );

      client.emit('room_joined', {
        roomType: data.type,
        roomName,
        identifiers: data.identifiers,
      });
    } catch (error) {
      this.logger.error('Error joining room:', error);
      client.emit('error', {
        code: 'JOIN_ROOM_FAILED',
        message: 'Failed to join room',
      });
    }
  }

  @SubscribeMessage('leave_room')
  async handleLeaveRoom(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: LeaveRoomDto,
  ) {
    try {
      const roomName = await this.roomManager.leaveRoom(
        client,
        RoomType[data.type.toUpperCase()],
        ...data.identifiers,
      );

      client.emit('room_left', {
        roomType: data.type,
        roomName,
        identifiers: data.identifiers,
      });
    } catch (error) {
      this.logger.error('Error leaving room:', error);
      client.emit('error', {
        code: 'LEAVE_ROOM_FAILED',
        message: 'Failed to leave room',
      });
    }
  }

  @SubscribeMessage('join_multiple_rooms')
  async handleJoinMultipleRooms(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: JoinMultipleRoomsDto,
  ) {
    try {
      const rooms = data.rooms.map((room) => ({
        type: RoomType[room.type.toUpperCase()],
        identifiers: room.identifiers,
      }));

      const joinedRooms = await this.roomManager.joinMultipleRooms(
        client,
        rooms,
      );

      client.emit('multiple_rooms_joined', {
        joinedRooms,
        count: joinedRooms.length,
      });
    } catch (error) {
      this.logger.error('Error joining multiple rooms:', error);
      client.emit('error', {
        code: 'JOIN_MULTIPLE_ROOMS_FAILED',
        message: 'Failed to join multiple rooms',
      });
    }
  }

  @SubscribeMessage('get_my_rooms')
  handleGetMyRooms(@ConnectedSocket() client: Socket) {
    const rooms = this.roomManager.getClientRooms(client);
    client.emit('my_rooms', { rooms });
  }

  @SubscribeMessage('send_message')
  async handleSendMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: SocketSendMessageDto,
  ) {
    try {
      await this.chatOperationHandler.handleSendMessage(client, {
        chatType: data.chatType,
        chatId: data.chatId,
        content: data.content,
        replyToMessageId: data.replyToMessageId,
        nonce: data.nonce,
        sentAt: data.sentAt,
        senderId: 0,
      });
    } catch (error) {
      client.emit('error', {
        code: 'SEND_MESSAGE_FAILED',
        message: 'Failed to send message',
        error: error.message,
      });
    }
  }

  @SubscribeMessage('user_typing')
  async handleUserTyping(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: UserTypingDto,
  ) {
    try {
      await this.chatOperationHandler.handleUserTyping(client, data);
    } catch (error) {
      client.emit('error', {
        code: 'USER_TYPING_FAILED',
        message: 'Failed to handle typing indicator',
      });
    }
  }

  @SubscribeMessage('message_read')
  async handleMessageRead(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: MessageReadDto,
  ) {
    try {
      await this.chatOperationHandler.handleMessageRead(client, data);
    } catch (error) {
      client.emit('error', {
        code: 'MESSAGE_READ_FAILED',
        message: 'Failed to mark message as read',
        error: error.message,
      });
    }
  }

  @SubscribeMessage('delete_message_for_me')
  async handleDeleteMessageForMe(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: DeleteMessageDto,
  ) {
    try {
      await this.chatOperationHandler.handleDeleteMessageForMe(client, data);
    } catch (error) {
      client.emit('error', {
        code: 'DELETE_MESSAGE_FOR_ME_FAILED',
        message: 'Failed to delete message for you',
        error: error.message,
      });
    }
  }

  @SubscribeMessage('delete_messages_for_everyone')
  async handleDeleteMessagesForEveryone(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: DeleteMessagesForEveryoneDto,
  ) {
    try {
      await this.chatOperationHandler.handleDeleteMessagesForEveryone(
        client,
        data,
      );
    } catch (error) {
      client.emit('error', {
        code: 'DELETE_MESSAGES_FOR_EVERYONE_FAILED',
        message: 'Failed to delete messages for everyone',
        error: error.message,
      });
    }
  }

  // ============== PRESENCE HANDLERS ==============

  @SubscribeMessage('ping')
  async handlePing(client: Socket) {
    return this.userPresenceHandler.handlePing(client);
  }

  @SubscribeMessage('get-status')
  async handleGetStatus(client: Socket, data: { memberId: number }) {
    return this.userPresenceHandler.handleGetStatus(client, data);
  }

  @SubscribeMessage('get-multiple-status')
  async handleGetMultipleStatus(client: Socket, data: { memberIds: number[] }) {
    return this.userPresenceHandler.handleGetMultipleStatus(client, data);
  }

  @SubscribeMessage('get-online-members')
  async handleGetOnlineMembers(client: Socket) {
    return this.userPresenceHandler.handleGetOnlineMembers(client);
  }

  @SubscribeMessage('get-member-devices')
  async handleGetMemberDevices(client: Socket, data: { memberId: number }) {
    return this.userPresenceHandler.handleGetMemberDevices(client, data);
  }

  @SubscribeMessage('force-refresh-status')
  async handleForceRefreshStatus(client: Socket) {
    return this.userPresenceHandler.handleForceRefreshStatus(client);
  }

  // ============== SYSTEM HANDLERS ==============

  @SubscribeMessage('health-check')
  async handleHealthCheck(client: Socket) {
    return this.systemHandler.handleHealthCheck(client, this.server);
  }

  @SubscribeMessage('debug-presence')
  async handleDebugPresence(client: Socket, data: { memberId: number }) {
    return this.systemHandler.handleDebugPresence(client, data);
  }

  // ============== PUBLIC METHODS FOR EXTERNAL USE ==============

  emitToRoom(
    roomType: RoomType,
    event: string,
    data: any,
    ...identifiers: (string | number)[]
  ) {
    this.roomManager.emitToRoom(
      this.server,
      roomType,
      event,
      data,
      ...identifiers,
    );
  }

  emitToRoomExcludingMember(
    roomType: RoomType,
    event: string,
    data: any,
    excludeMemberId: number,
    ...identifiers: (string | number)[]
  ) {
    const roomName = this.roomManager.generateRoomName(
      roomType,
      ...identifiers,
    );
    this.server
      .to(roomName)
      .except(`member_${excludeMemberId}`)
      .emit(event, data);
  }

  broadcastToMember(memberId: number, event: string, data: any) {
    this.roomManager.emitToRoom(
      this.server,
      RoomType.MEMBER,
      event,
      data,
      memberId,
    );
  }

  emitToGroup(groupId: number, event: string, data: any) {
    this.roomManager.emitToRoom(
      this.server,
      RoomType.GROUP,
      event,
      data,
      groupId,
    );
  }

  emitToPrivateChat(
    member1Id: number,
    member2Id: number,
    event: string,
    data: any,
  ) {
    this.roomManager.emitToRoom(
      this.server,
      RoomType.PRIVATE,
      event,
      data,
      member1Id,
      member2Id,
    );
  }

  emitToOrganization(organizationId: number, event: string, data: any) {
    this.roomManager.emitToRoom(
      this.server,
      RoomType.ORGANIZATION,
      event,
      data,
      organizationId,
    );
  }

  async getConnectedClientsCount(): Promise<number> {
    const sockets = await this.server.fetchSockets();
    return sockets.length;
  }

  async getRoomStats(
    roomType: RoomType,
    ...identifiers: (string | number)[]
  ): Promise<number> {
    return this.roomManager.getRoomClientsCount(
      this.server,
      roomType,
      ...identifiers,
    );
  }
}
