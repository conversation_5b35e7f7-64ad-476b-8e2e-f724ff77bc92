import { Injectable } from '@nestjs/common';
import { In, QueryRunner, Repository } from 'typeorm';
import { PrivateMessage } from '../entities/private-message.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { SendPrivateMessageDto } from '../dto/send-private-message.dto';

@Injectable()
export class PrivateMessageService {
  constructor(
    @InjectRepository(PrivateMessage)
    private readonly privateMessageRepo: Repository<PrivateMessage>,
  ) {}

  /** Get next sequence number for a private chat between two members */
  async getNextSequenceNumber(
    member1Id: number,
    member2Id: number,
    queryRunner: QueryRunner,
  ): Promise<number> {
    const result = await queryRunner.manager
      .createQueryBuilder(PrivateMessage, 'pm')
      .select('COALESCE(MAX(pm.seq), 0)', 'maxSeq')
      .where(
        '(pm.senderId = :m1 AND pm.receiverId = :m2) OR (pm.senderId = :m2 AND pm.receiverId = :m1)',
        { m1: member1Id, m2: member2Id },
      )
      .getRawOne<{ maxSeq: number }>();

    return (result?.maxSeq || 0) + 1;
  }

  /** Create a private message (welcome or normal) */
  async createPrivateMessage(
    senderId: number,
    receiverId: number,
    content: string,
    seq: number,
    queryRunner: QueryRunner,
    dto: SendPrivateMessageDto,
  ): Promise<PrivateMessage> {
    const message = this.privateMessageRepo.create({
      sender: { id: dto.senderId },
      receiver: { id: dto.receiverId },
      encryptedContent: dto.encryptedContent,
      replyToMessageId: dto.replyToMessageId,
      file: dto.fileId ? { id: dto.fileId } : undefined,
      sentAt: dto.sentAt || new Date(),
      acknowledged: false,
      messageIndex: dto.messageIndex ?? 1,
      previousChainLength: dto.previousChainLength ?? 0,
      chainKeyVersion: dto.chainKeyVersion ?? 1,
    });

    return (await this.privateMessageRepo.save(message)) as PrivateMessage;
  }

  /** Create a private welcome message from system/admin */
  async createPrivateWelcomeMessage(
    senderId: number,
    receiverId: number,
    content: string,
    seq: number,
    queryRunner: QueryRunner,
  ): Promise<PrivateMessage> {
    const dto: SendPrivateMessageDto = {
      senderId,
      receiverId,
      encryptedContent: content,
      sentAt: new Date(),
      nonce: 'welcome_nonce',
      messageIndex: 1,
      previousChainLength: 0,
      chainKeyVersion: 1,
    };
    return this.createPrivateMessage(
      senderId,
      receiverId,
      content,
      seq,
      queryRunner,
      dto,
    );
  }

  /** Find one message with sender, receiver, and file relations */
  async findOneMessage(id: number): Promise<PrivateMessage | null> {
    return this.privateMessageRepo.findOne({
      where: { id },
      relations: ['sender', 'receiver', 'file'],
    });
  }

  /** Get last message in a private thread between two members */
  async getLastMessageForThread(
    member1Id: number,
    member2Id: number,
  ): Promise<PrivateMessage | null> {
    return this.privateMessageRepo.findOne({
      where: [
        { senderId: member1Id, receiverId: member2Id },
        { senderId: member2Id, receiverId: member1Id },
      ],
      relations: ['sender', 'receiver', 'file'],
      order: { seq: 'DESC' },
    });
  }

  /** Create private chats for a new member with all existing members */
  async createPrivateChatsForNewMember(
    queryRunner: QueryRunner,
    newMemberId: number,
    existingMemberIds: number[],
    welcomeMessageContent: string,
  ): Promise<void> {
    for (const otherMemberId of existingMemberIds) {
      const seq = await this.getNextSequenceNumber(
        newMemberId,
        otherMemberId,
        queryRunner,
      );
      await this.createPrivateWelcomeMessage(
        newMemberId,
        otherMemberId,
        welcomeMessageContent,
        seq,
        queryRunner,
      );

      const seqReverse = await this.getNextSequenceNumber(
        otherMemberId,
        newMemberId,
        queryRunner,
      );
      await this.createPrivateWelcomeMessage(
        otherMemberId,
        newMemberId,
        welcomeMessageContent,
        seqReverse,
        queryRunner,
      );
    }
  }

  /** Deactivate private chats when a member leaves a group */
  async deactivatePrivateChatsForMember(
    queryRunner: QueryRunner,
    memberId: number,
    otherMemberIds: number[],
  ): Promise<void> {
    const messages = await queryRunner.manager.find(PrivateMessage, {
      where: [
        { senderId: memberId, receiverId: In(otherMemberIds) },
        { senderId: In(otherMemberIds), receiverId: memberId },
      ],
    });

    for (const msg of messages) {
      msg.acknowledged = false; // optional: can also add `isActive` flag in entity if needed
      await queryRunner.manager.save(msg);
    }
  }
}
