import { Injectable } from '@nestjs/common';
import { In, QueryRunner, Repository } from 'typeorm';
import { PrivateMessage } from '../entities/private-message.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { SendPrivateMessageDto } from '../dto/send-private-message.dto';
import { ChatType } from '../../../infrastructure/socket/dto/chat-operation.dto';
import { MessageSentEvent } from 'src/common/events';
import { EventBusService } from 'src/infrastructure/events/event-bus.service';
import {
  MemberNotificationInfo,
  PushNotification,
} from 'src/infrastructure/notification/services/notification.service';

@Injectable()
export class PrivateMessageService {
  constructor(
    @InjectRepository(PrivateMessage)
    private readonly privateMessageRepo: Repository<PrivateMessage>,
    private readonly eventBus: EventBusService,
  ) {}

  /** Get next sequence number for a private chat between two members */
  async getNextSequenceNumber(
    member1Id: number,
    member2Id: number,
    queryRunner: QueryRunner,
  ): Promise<number> {
    const result = await queryRunner.manager
      .createQueryBuilder(PrivateMessage, 'pm')
      .select('COALESCE(MAX(pm.seq), 0)', 'maxSeq')
      .where(
        '(pm.senderId = :m1 AND pm.receiverId = :m2) OR (pm.senderId = :m2 AND pm.receiverId = :m1)',
        { m1: member1Id, m2: member2Id },
      )
      .getRawOne<{ maxSeq: number }>();

    return (result?.maxSeq || 0) + 1;
  }

  /** Create a private message (welcome or normal) */
  async createPrivateMessage(
    senderId: number,
    receiverId: number,
    content: string,
    seq: number,
    queryRunner: QueryRunner,
    dto: SendPrivateMessageDto,
  ): Promise<PrivateMessage> {
    const message = this.privateMessageRepo.create({
      seq: seq,
      senderId: dto.senderId,
      receiverId: dto.receiverId,
      sender: { id: dto.senderId },
      receiver: { id: dto.receiverId },
      encryptedContent: dto.encryptedContent,
      replyToMessageId: dto.replyToMessageId,
      file: dto.fileId ? { id: dto.fileId } : undefined,
      sentAt: dto.sentAt || new Date(),
      acknowledged: false,
      messageIndex: dto.messageIndex ?? 1,
      previousChainLength: dto.previousChainLength ?? 0,
      chainKeyVersion: dto.chainKeyVersion ?? 1,
    });

    return await queryRunner.manager.save(PrivateMessage, message);
  }

  /** Create a private welcome message from system/admin */
  async createPrivateWelcomeMessage(
    senderId: number,
    receiverId: number,
    content: string,
    seq: number,
    queryRunner: QueryRunner,
  ): Promise<PrivateMessage> {
    // Sort IDs to ensure consistent chat ID format
    const sortedIds = [senderId, receiverId].sort((a, b) => a - b);
    const chatId = sortedIds.join('_');

    const dto: SendPrivateMessageDto = {
      chatType: ChatType.PRIVATE,
      chatId,
      senderId,
      receiverId,
      encryptedContent: content,
      sentAt: new Date(),
      nonce: 'welcome_nonce',
      messageIndex: 1,
      previousChainLength: 0,
      chainKeyVersion: 1,
    };
    return this.createPrivateMessage(
      senderId,
      receiverId,
      content,
      seq,
      queryRunner,
      dto,
    );
  }

  /** Find one message with sender, receiver, and file relations */
  async findOneMessage(id: number): Promise<PrivateMessage | null> {
    return this.privateMessageRepo.findOne({
      where: { id },
      relations: ['sender', 'receiver', 'file'],
    });
  }

  /** Get last message in a private thread between two members */
  async getLastMessageForThread(
    member1Id: number,
    member2Id: number,
  ): Promise<PrivateMessage | null> {
    return this.privateMessageRepo.findOne({
      where: [
        { senderId: member1Id, receiverId: member2Id },
        { senderId: member2Id, receiverId: member1Id },
      ],
      relations: ['sender', 'receiver', 'file'],
      order: { seq: 'DESC' },
    });
  }

  /** Create private chats for a new member with all existing members */
  async createPrivateChatsForNewMember(
    queryRunner: QueryRunner,
    newMemberId: number,
    existingMemberIds: number[],
    welcomeMessageContent: string,
  ): Promise<void> {
    for (const otherMemberId of existingMemberIds) {
      const seq = await this.getNextSequenceNumber(
        newMemberId,
        otherMemberId,
        queryRunner,
      );
      await this.createPrivateWelcomeMessage(
        newMemberId,
        otherMemberId,
        welcomeMessageContent,
        seq,
        queryRunner,
      );

      const seqReverse = await this.getNextSequenceNumber(
        otherMemberId,
        newMemberId,
        queryRunner,
      );
      await this.createPrivateWelcomeMessage(
        otherMemberId,
        newMemberId,
        welcomeMessageContent,
        seqReverse,
        queryRunner,
      );
    }
  }

  /** Deactivate private chats when a member leaves a group */
  async deactivatePrivateChatsForMember(
    queryRunner: QueryRunner,
    memberId: number,
    otherMemberIds: number[],
  ): Promise<void> {
    const messages = await queryRunner.manager.find(PrivateMessage, {
      where: [
        { senderId: memberId, receiverId: In(otherMemberIds) },
        { senderId: In(otherMemberIds), receiverId: memberId },
      ],
    });

    for (const msg of messages) {
      msg.acknowledged = false; // optional: can also add `isActive` flag in entity if needed
      await queryRunner.manager.save(msg);
    }
  }

  /** Save and broadcast a private message */
  async saveAndBroadcastMessage(
    sendMessageDto: SendPrivateMessageDto,
    excludeSenderId?: boolean,
  ): Promise<PrivateMessage> {
    // Use a transaction to ensure consistency
    return await this.privateMessageRepo.manager.connection.transaction(
      async (entityManager) => {
        // Create a QueryRunner from the entity manager
        const queryRunner = entityManager.queryRunner!;

        // Get next sequence number
        const seq = await this.getNextSequenceNumber(
          sendMessageDto.senderId,
          sendMessageDto.receiverId,
          queryRunner,
        );

        // Save the message
        const savedMessage = await this.createPrivateMessage(
          sendMessageDto.senderId,
          sendMessageDto.receiverId,
          sendMessageDto.encryptedContent,
          seq,
          queryRunner,
          sendMessageDto,
        );

        // Get the saved message with relations
        const messageWithRelations = await entityManager.findOne(
          PrivateMessage,
          {
            where: { id: savedMessage.id },
            relations: ['sender', 'receiver', 'file'],
          },
        );

        if (!messageWithRelations) {
          throw new Error('Failed to retrieve saved message');
        }

        // Prepare target members (sender and receiver, optionally excluding sender)
        const targetMembers: number[] = excludeSenderId
          ? [sendMessageDto.receiverId]
          : [sendMessageDto.senderId, sendMessageDto.receiverId];

        // Prepare notification info (for now, simple structure)
        const memberNotificationInfo = targetMembers.map((memberId) => ({
          memberId,
          silent: false, // TODO: Add mute functionality for private chats
        }));

        // Prepare message data for broadcasting
        const messageData = {
          id: messageWithRelations.id,
          seq: messageWithRelations.seq,
          senderId: messageWithRelations.senderId,
          receiverId: messageWithRelations.receiverId,
          encryptedContent: messageWithRelations.encryptedContent,
          messageIndex: messageWithRelations.messageIndex,
          previousChainLength: messageWithRelations.previousChainLength,
          chainKeyVersion: messageWithRelations.chainKeyVersion,
          sentAt: messageWithRelations.sentAt,
          sender: messageWithRelations.sender,
          receiver: messageWithRelations.receiver,
          file: messageWithRelations.file,
        };

        // Prepare notification payload
        const notificationPayload: PushNotification = {
          title: messageWithRelations.sender.name,
          body: 'sent you a message',
          data: messageData,
          imageUrl: undefined, // TODO: Add profile image support
        };

        // Publish event for message delivery
        this.eventBus.publish(
          new MessageSentEvent(
            messageWithRelations.id,
            messageWithRelations.senderId,
            memberNotificationInfo,
            notificationPayload,
            0, // retry count
          ),
        );

        return messageWithRelations;
      },
    );
  }
}
