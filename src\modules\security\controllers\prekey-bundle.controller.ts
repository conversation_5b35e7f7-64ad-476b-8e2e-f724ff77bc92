import {
  Controller,
  Post,
  Body,
  Param,
  Get,
  NotFoundException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { PreKeyBundleService } from '../services/prekey-bundleService';
import { CreateSignedPreKeyDto } from '../dtos/create-signed-prekey.dto';

@ApiTags('PreKey Bundle')
@Controller('members/:memberId/prekey-bundle')
export class PreKeyBundleController {
  constructor(private readonly preKeyBundleService: PreKeyBundleService) {}

  @Post('signed-prekeys')
  @ApiOperation({ summary: 'Upload signed prekey and get prekey bundle' })
  @ApiParam({ name: 'memberId', type: 'string', description: 'OrgMember ID' })
  @ApiResponse({
    status: 201,
    description: 'Signed prekey saved and one-time prekeys returned',
  })
  async createPreKeyBundle(
    @Param('memberId') memberId: string,
    @Body() dto: CreateSignedPreKeyDto,
  ) {
    return this.preKeyBundleService.createPreKeyBundle(memberId, dto);
  }

  @Get('signed-prekeys')
  @ApiOperation({ summary: 'Get all signed prekeys for a member' })
  @ApiParam({ name: 'memberId', type: 'string', description: 'OrgMember ID' })
  @ApiResponse({ status: 200, description: 'List of signed prekeys returned' })
  async findAllSignedPreKeys(@Param('memberId') memberId: string) {
    return this.preKeyBundleService.findAllSignedPreKeys(memberId);
  }

  @Get('signed-prekeys/:keyId')
  @ApiOperation({ summary: 'Get a specific signed prekey by keyId' })
  @ApiParam({ name: 'memberId', type: 'string', description: 'OrgMember ID' })
  @ApiParam({
    name: 'keyId',
    type: 'number',
    description: 'SignedPreKey keyId',
  })
  @ApiResponse({ status: 200, description: 'Signed prekey found' })
  @ApiResponse({ status: 404, description: 'Signed prekey not found' })
  async findOneSignedPreKey(
    @Param('memberId') memberId: string,
    //@Param('keyId') keyId: number,
  ) {
    return this.preKeyBundleService.getPreKeyBundle(memberId);
  }
}
